import { forwardRef, useEffect, useState } from "react"

import { Close } from "../common/Close"
import { DeleteOutlined } from "../common/DeleteOutlined"
import { PaperClip } from "../common/PaperClip"
import { IconButton } from "../icon-button"
import { Typography } from "../typography"
import styles from "./uploadBox.module.css"

export type UploadedFileItemProps = {
  name: string
  onDelete?: () => void
  onCancelUpload?: () => void
  uploading?: boolean
  errorMessage?: string
  disabled?: boolean
  className?: string
}

const UploadedFileItem = forwardRef<HTMLDivElement, UploadedFileItemProps>(
  function UploadedFileItemRoot(
    {
      className,
      name,
      onCancelUpload,
      uploading,
      onDelete,
      errorMessage,
      disabled,
    },
    ref
  ) {
    return (
      <div
        className={`${styles.uploadBox} ${styles.uploadedFileItem} ${errorMessage ? styles.uploadedFileItemError : ""} ${className || ""}`}
        ref={ref}
      >
        <div className={styles.uploadedFileItemContent}>
          <div className={styles.uploadedFileItemInfoContainer}>
            <div className={styles.uploadedFileItemInfo}>
              <PaperClip
                width={16}
                height={16}
                className={styles.uploadedFileItemIcon}
              />
              <Typography level="bodyLarge">{name}</Typography>
            </div>
            {uploading ? (
              <Typography className={styles.uploadingText} level="bodySmall">
                Uploading
              </Typography>
            ) : null}
            {errorMessage ? (
              <Typography level="bodySmall" color="negative">
                {errorMessage}
              </Typography>
            ) : null}
          </div>
          {disabled ? null : (
            <IconButton
              size="large"
              onClick={uploading ? onCancelUpload : onDelete}
            >
              {uploading ? (
                <Close width={24} height={24} />
              ) : (
                <DeleteOutlined
                  width={24}
                  height={24}
                  className={styles.deleteIcon}
                />
              )}
            </IconButton>
          )}
        </div>
        {uploading ? <LinearLoadingIndicator /> : null}
      </div>
    )
  }
)

UploadedFileItem.displayName = "UploadedFileItem"

export default UploadedFileItem

function LinearLoadingIndicator() {
  const [progress, setProgress] = useState(0)

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prevProgress) =>
        prevProgress >= 100 ? 0 : prevProgress + 10
      )
    }, 1000) // Updates every second

    return () => clearInterval(interval)
  }, [])

  return (
    <div className={styles.loadingIndicatorContainer}>
      <div
        className={styles.loadingIndicator}
        style={{
          width: `${progress}%`,
        }}
      />
    </div>
  )
}
